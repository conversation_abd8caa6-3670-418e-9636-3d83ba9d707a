// ◀︎ LLM-modified - New morphing trigger component for CommandMenu with layout animations
import React, { useState, useEffect } from 'react';
import { motion, Variants } from 'framer-motion';
import { Command } from 'lucide-react';

interface CommandMenuTriggerProps {
  onClick: () => void;
  variant?: 'header' | 'floating';
  className?: string;
  children?: React.ReactNode;
  'aria-label'?: string;
  'aria-keyshortcuts'?: string;
}

// Animation variants for trigger button
const triggerVariants: Variants = {
  idle: {
    scale: 1,
    y: 0,
  },
  hover: {
    scale: 1.05,
    y: -1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
    },
  },
  tap: {
    scale: 0.98,
    y: 0,
    transition: { duration: 0.1 },
  },
};

export const CommandMenuTrigger: React.FC<CommandMenuTriggerProps> = ({
  onClick,
  variant = 'header',
  className = '',
  children,
  'aria-label': ariaLabel = 'Open command menu',
  'aria-keyshortcuts': ariaKeyshortcuts,
}) => {
  const [isMac, setIsMac] = useState<boolean>(false);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState<boolean>(false);

  // Detect OS for keyboard shortcut display
  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0);
  }, []);

  // Detect user's motion preferences for accessibility
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent): void => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Handle keyboard interaction
  const handleKeyDown = (e: React.KeyboardEvent): void => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };

  // Base styles for different variants
  const getVariantStyles = (): string => {
    switch (variant) {
      case 'header':
        return 'command-button flex cursor-pointer items-center rounded-full border border-white/20 px-4 py-3 text-white shadow-lg backdrop-blur-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75';
      case 'floating':
        return 'flex items-center justify-center rounded-full text-white shadow-lg transition-all duration-300 bg-gradient-button hover:bg-gradient-button-hover focus:outline-none focus-visible:ring-2 focus-visible:ring-token-primary-500 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-token-secondary-900';
      default:
        return '';
    }
  };

  // Get inline styles for different variants
  const getVariantInlineStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      willChange: 'transform, opacity',
    };

    switch (variant) {
      case 'header':
        return {
          ...baseStyles,
          background: 'var(--token-bg-frosted-strong)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
          backgroundImage: 'var(--gradient-button)',
          backgroundSize: '200% auto',
        };
      case 'floating':
        return {
          ...baseStyles,
          width: 'clamp(2.75rem, 4vw, 3.5rem)',
          height: 'clamp(2.75rem, 4vw, 3.5rem)',
        };
      default:
        return baseStyles;
    }
  };

  // Render header variant content
  const renderHeaderContent = (): React.ReactNode => (
    <>
      <Command size={18} className='mr-3 text-white/90' />
      <span className='flex-1 text-left text-white/90'>
        {children || 'Open Command Menu'}
      </span>
      <kbd className='hidden items-center justify-center rounded bg-white/10 px-2 py-1 text-xs text-white/80 md:flex'>
        {ariaKeyshortcuts || (isMac ? '⌘K' : 'Ctrl+K')}
      </kbd>
    </>
  );

  // Render floating variant content
  const renderFloatingContent = (): React.ReactNode => (
    <Command
      style={{
        width: 'clamp(1rem, 2.5vw, 1.5rem)',
        height: 'clamp(1rem, 2.5vw, 1.5rem)',
      }}
    />
  );

  return (
    <motion.div
      layoutId="command-menu-container"
      className={`${getVariantStyles()} ${className}`}
      style={getVariantInlineStyles()}
      variants={triggerVariants}
      initial="idle"
      whileHover={prefersReducedMotion ? undefined : "hover"}
      whileTap={prefersReducedMotion ? undefined : "tap"}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      aria-label={ariaLabel}
      aria-keyshortcuts={ariaKeyshortcuts || (isMac ? '⌘+K' : 'Ctrl+K')}
    >
      {variant === 'header' ? renderHeaderContent() : renderFloatingContent()}
    </motion.div>
  );
};

export default CommandMenuTrigger;
